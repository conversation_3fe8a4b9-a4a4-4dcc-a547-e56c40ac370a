using Microsoft.Extensions.Logging;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Services;

public class VideoAnalyzer : IVideoAnalyzer
{
    private readonly IVideoFrameExtractor _frameExtractor;
    private readonly IAIAnalysisService _aiAnalysisService;
    private readonly ISubtitleService _subtitleService;
    private readonly IPlaceDetectionService _placeDetectionService;
    private readonly ILogger<VideoAnalyzer> _logger;

    public VideoAnalyzer(
        IVideoFrameExtractor frameExtractor,
        IAIAnalysisService aiAnalysisService,
        ISubtitleService subtitleService,
        IPlaceDetectionService placeDetectionService,
        ILogger<VideoAnalyzer> logger)
    {
        _frameExtractor = frameExtractor;
        _aiAnalysisService = aiAnalysisService;
        _subtitleService = subtitleService;
        _placeDetectionService = placeDetectionService;
        _logger = logger;
    }

    public async Task<VideoAnalysisResult> AnalyzeVideoAsync(VideoAnalysisRequest request, CancellationToken cancellationToken = default)
    {
        return await AnalyzeVideoAsync(request, null, cancellationToken);
    }

    public async Task<VideoAnalysisResult> AnalyzeVideoAsync(
        VideoAnalysisRequest request, 
        IProgress<AnalysisProgress>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting video analysis for: {VideoPath}", request.VideoPath);
        var startTime = DateTime.UtcNow;
        
        if (!File.Exists(request.VideoPath))
        {
            throw new FileNotFoundException($"Video file not found: {request.VideoPath}");
        }

        var result = new VideoAnalysisResult
        {
            VideoPath = request.VideoPath,
            AnalysisTimestamp = startTime
        };

        try
        {
            // Step 1: Get video duration
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Analyzing video metadata", 
                ProgressPercentage = 0,
                Message = "Getting video duration...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.VideoDuration = await _frameExtractor.GetVideoDurationAsync(request.VideoPath, cancellationToken);
            _logger.LogInformation("Video duration: {Duration}", result.VideoDuration);

            // Step 2: Extract frames
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Extracting video frames", 
                ProgressPercentage = 10,
                Message = "Extracting key frames from video...",
                Elapsed = DateTime.UtcNow - startTime
            });

            var extractedFrames = await _frameExtractor.ExtractFramesAsync(request.VideoPath, request.Options, cancellationToken);
            _logger.LogInformation("Extracted {Count} frames", extractedFrames.Count);

            // Step 3: Process subtitles
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Processing subtitles", 
                ProgressPercentage = 25,
                Message = "Loading or generating subtitles...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.SubtitleSegments = await ProcessSubtitlesAsync(request, cancellationToken);
            _logger.LogInformation("Processed {Count} subtitle segments", result.SubtitleSegments.Count);

            // Step 4: Analyze frames with AI
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "AI Frame Analysis", 
                ProgressPercentage = 40,
                Message = "Analyzing video frames with AI...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.FrameAnalyses = await AnalyzeFramesAsync(extractedFrames, request.Options, progress, startTime, cancellationToken);
            _logger.LogInformation("Completed AI analysis for {Count} frames", result.FrameAnalyses.Count);

            // Step 5: Analyze subtitle content
            if (result.SubtitleSegments.Any())
            {
                progress?.Report(new AnalysisProgress 
                { 
                    CurrentStage = "Subtitle Analysis", 
                    ProgressPercentage = 80,
                    Message = "Analyzing subtitle content...",
                    Elapsed = DateTime.UtcNow - startTime
                });

                await AnalyzeSubtitleContentAsync(result.SubtitleSegments, cancellationToken);
            }

            // Step 6: Generate summary
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Generating Summary", 
                ProgressPercentage = 90,
                Message = "Generating video summary...",
                Elapsed = DateTime.UtcNow - startTime
            });

            result.Summary = await GenerateSummaryAsync(result.FrameAnalyses, result.SubtitleSegments, cancellationToken);

            // Step 6.5: Calculate performance metrics
            result.PerformanceMetrics = CalculatePerformanceMetrics(result.FrameAnalyses, startTime, DateTime.UtcNow);

            // Step 7: Complete
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Complete", 
                ProgressPercentage = 100,
                Message = "Analysis completed successfully",
                Elapsed = DateTime.UtcNow - startTime
            });

            _logger.LogInformation("Video analysis completed successfully in {Elapsed}", DateTime.UtcNow - startTime);
            _logger.LogInformation("Performance metrics - Avg frame analysis: {AvgTime}ms, Total frames: {Total}, Success rate: {SuccessRate}%",
                result.PerformanceMetrics.AverageFrameAnalysisTime.TotalMilliseconds,
                result.PerformanceMetrics.TotalFramesAnalyzed,
                result.PerformanceMetrics.TotalFramesAnalyzed > 0 ? (result.PerformanceMetrics.SuccessfulAnalyses * 100.0 / result.PerformanceMetrics.TotalFramesAnalyzed) : 0);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during video analysis: {VideoPath}", request.VideoPath);
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "Error", 
                ProgressPercentage = 0,
                Message = $"Analysis failed: {ex.Message}",
                Elapsed = DateTime.UtcNow - startTime
            });
            throw;
        }
    }

    private async Task<List<SubtitleSegment>> ProcessSubtitlesAsync(VideoAnalysisRequest request, CancellationToken cancellationToken)
    {
        if (!string.IsNullOrEmpty(request.SubtitlePath) && File.Exists(request.SubtitlePath))
        {
            _logger.LogInformation("Loading subtitles from file: {SubtitlePath}", request.SubtitlePath);
            
            if (await _subtitleService.IsSubtitleFileValidAsync(request.SubtitlePath, cancellationToken))
            {
                return await _subtitleService.ParseSubtitleFileAsync(request.SubtitlePath, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Invalid subtitle file, will generate subtitles instead");
            }
        }

        if (request.Options.GenerateSubtitlesIfMissing)
        {
            _logger.LogInformation("Generating subtitles using Whisper");
            return await _subtitleService.GenerateSubtitlesAsync(request.VideoPath, cancellationToken);
        }

        return [];
    }

    private async Task<List<FrameAnalysis>> AnalyzeFramesAsync(
        List<ExtractedFrame> extractedFrames, 
        VideoAnalysisOptions options,
        IProgress<AnalysisProgress>? progress,
        DateTime startTime,
        CancellationToken cancellationToken)
    {
        var frameAnalyses = new List<FrameAnalysis>();
        var totalFrames = extractedFrames.Count;

        // Process frames in batches to avoid overwhelming the AI service
        const int batchSize = 5;
        var batches = extractedFrames
            .Select((frame, index) => new { Frame = frame, Index = index })
            .GroupBy(x => x.Index / batchSize)
            .Select(g => g.Select(x => x.Frame).ToList())
            .ToList();

        for (int batchIndex = 0; batchIndex < batches.Count; batchIndex++)
        {
            var batch = batches[batchIndex];
            var batchProgress = 40 + (int)((double)(batchIndex * batchSize) / totalFrames * 35);
            
            progress?.Report(new AnalysisProgress 
            { 
                CurrentStage = "AI Frame Analysis", 
                ProgressPercentage = batchProgress,
                Message = $"Analyzing frames {batchIndex * batchSize + 1}-{Math.Min((batchIndex + 1) * batchSize, totalFrames)} of {totalFrames}",
                Elapsed = DateTime.UtcNow - startTime
            });

            var batchTasks = batch.Select(async frame =>
            {
                try
                {
                    var frameAnalysis = new FrameAnalysis
                    {
                        Timestamp = frame.Timestamp,
                        FramePath = frame.FramePath,
                        AnalysisStartTime = DateTime.UtcNow
                    };

                    // Analyze scene with timing
                    var (sceneResult, sceneDuration) = await _aiAnalysisService.AnalyzeFrameWithTimingAsync(frame.FramePath, cancellationToken);
                    frameAnalysis.Scene = sceneResult;
                    frameAnalysis.ConfidenceScore = 0.8; // Default confidence

                    // Extract text if enabled
                    TimeSpan textDuration = TimeSpan.Zero;
                    if (options.EnableTextRecognition)
                    {
                        var textStartTime = DateTime.UtcNow;
                        frameAnalysis.DetectedTexts = await _aiAnalysisService.ExtractTextFromFrameAsync(frame.FramePath, cancellationToken);
                        textDuration = DateTime.UtcNow - textStartTime;
                    }

                    // Extract place information to populate PlaceInfos
                    TimeSpan placeInfoDuration = TimeSpan.Zero;
                    if (options.EnablePlaceRecognition)
                    {
                        var placeInfoStartTime = DateTime.UtcNow;
                        frameAnalysis.PlaceInfos = await _aiAnalysisService.ExtractPlaceInfoAsync(frame.FramePath, cancellationToken);
                        placeInfoDuration = DateTime.UtcNow - placeInfoStartTime;
                    }
                    else
                    {
                        // Even without place recognition enabled, try to extract basic restaurant info from scene analysis
                        frameAnalysis.PlaceInfos = ExtractBasicPlaceInfoFromScene(frameAnalysis.Scene);
                    }

                    // Set timing information
                    frameAnalysis.AnalysisEndTime = DateTime.UtcNow;
                    frameAnalysis.AnalysisDuration = sceneDuration + textDuration + placeInfoDuration;

                    return frameAnalysis;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze frame at {Timestamp}", frame.Timestamp);
                    return new FrameAnalysis
                    {
                        Timestamp = frame.Timestamp,
                        FramePath = frame.FramePath,
                        Scene = new SceneDescription { MainDescription = "Analysis failed" },
                        ConfidenceScore = 0.0,
                        AnalysisStartTime = DateTime.UtcNow,
                        AnalysisEndTime = DateTime.UtcNow,
                        AnalysisDuration = TimeSpan.Zero,
                        PlaceInfos = []
                    };
                }
            });

            var batchResults = await Task.WhenAll(batchTasks);
            frameAnalyses.AddRange(batchResults);

            // Longer delay between batches for vision model processing
            await Task.Delay(2000, cancellationToken);
        }

        return frameAnalyses;
    }

    /// <summary>
    /// 從場景分析結果中提取基本的餐廳資訊
    /// </summary>
    private List<PlaceInfo> ExtractBasicPlaceInfoFromScene(SceneDescription scene)
    {
        var placeInfos = new List<PlaceInfo>();
        
        // 檢查是否為餐廳場景
        if (string.IsNullOrEmpty(scene.RestaurantCategory) || scene.RestaurantCategory == "非餐廳")
            return placeInfos;

        // 從 visibleTexts 中尋找可能的餐廳名稱
        var potentialRestaurantNames = scene.VisibleTexts
            .Where(text => !string.IsNullOrWhiteSpace(text) && 
                          text.Length > 1 && 
                          text.Length < 50 && // 避免過長的文字
                          IsLikelyRestaurantName(text))
            .ToList();

        foreach (var name in potentialRestaurantNames)
        {
            placeInfos.Add(new PlaceInfo
            {
                Name = name,
                Category = scene.RestaurantCategory != "非餐廳" ? scene.RestaurantCategory : scene.CuisineType,
                Description = scene.MainDescription,
                Confidence = 0.7, // 中等信心度，因為是從場景分析推斷的
                OriginalTexts = [name],
                Address = ExtractAddressFromSetting(scene.Setting),
                BusinessHours = null,
                Phone = null,
                Website = null
            });
        }

        return placeInfos;
    }

    /// <summary>
    /// 判斷文字是否可能是餐廳名稱
    /// </summary>
    private static bool IsLikelyRestaurantName(string text)
    {
        // 排除純數字、單字元、常見非名稱文字
        if (text.All(char.IsDigit) || 
            text.Length <= 1 ||
            text.All(c => char.IsWhiteSpace(c) || char.IsPunctuation(c)))
            return false;

        // 排除明顯的非名稱文字
        var excludePatterns = new[] { "第", "位", "名", "営業中", "閉店", "open", "close", "menu", "price" };
        if (excludePatterns.Any(pattern => text.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
            return false;

        // 包含餐廳相關關鍵字的更可能是店名
        var restaurantKeywords = new[] { "麺", "麵", "食堂", "店", "屋", "亭", "館", "家", "房", "kitchen", "cafe", "restaurant", "grill", "bar" };
        if (restaurantKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            return true;

        // 其他可能包含店名的模式（如日文、中文店名）
        return text.Any(c => char.GetUnicodeCategory(c) == System.Globalization.UnicodeCategory.OtherLetter);
    }

    /// <summary>
    /// 從設定描述中提取地址資訊
    /// </summary>
    private static string? ExtractAddressFromSetting(string setting)
    {
        if (string.IsNullOrWhiteSpace(setting))
            return null;

        // 尋找包含地址關鍵字的句子
        var addressKeywords = new[] { "位於", "地址", "地點", "附近", "區", "市", "街", "路", "道", "丁目", "番地" };
        if (addressKeywords.Any(keyword => setting.Contains(keyword)))
        {
            // 如果設定中包含地址相關資訊，返回簡化的地址描述
            return setting;
        }

        return null;
    }

    private async Task AnalyzeSubtitleContentAsync(List<SubtitleSegment> subtitleSegments, CancellationToken cancellationToken)
    {
        var analysisTasks = subtitleSegments
            .Where(s => !string.IsNullOrWhiteSpace(s.Text))
            .Take(50) // Limit to avoid token limits
            .Select(async segment =>
            {
                try
                {
                    segment.Analysis = await _aiAnalysisService.AnalyzeSubtitleSegmentAsync(segment.Text, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to analyze subtitle segment: {Text}", segment.Text);
                    segment.Analysis = new SubtitleAnalysis();
                }
            });

        await Task.WhenAll(analysisTasks);
    }

    private async Task<VideoSummary> GenerateSummaryAsync(
        List<FrameAnalysis> frameAnalyses, 
        List<SubtitleSegment> subtitleSegments, 
        CancellationToken cancellationToken)
    {
        try
        {
            var summary = new VideoSummary();

            // Generate overall description with subtitle content
            var (summaryText, subtitleContent, subtitleSegmentsForAnalysis) = await _aiAnalysisService.GenerateVideoSummaryAsync(frameAnalyses, subtitleSegments, cancellationToken);
            summary.OverallDescription = summaryText;
            summary.SubtitleContentForAnalysis = subtitleContent;
            summary.SubtitleSegmentsForAnalysis = subtitleSegmentsForAnalysis;

            // 生成截圖分析結果摘要
            summary.FrameAnalysisResult = GenerateFrameAnalysisSummary(frameAnalyses);
            
            // 生成字幕分析結果摘要
            summary.SubtitleAnalysisResult = GenerateSubtitleAnalysisSummary(subtitleSegments);

            // 保留原有的總體統計資料（合併版本）
            summary.MainActivities = frameAnalyses
                .SelectMany(f => f.Scene.Activities)
                .GroupBy(a => a.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(10)
                .Select(g => g.Key)
                .ToList();

            summary.DetectedObjects = frameAnalyses
                .SelectMany(f => f.Scene.Objects)
                .GroupBy(o => o.Name.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(15)
                .Select(g => g.Key)
                .ToList();

            summary.DetectedText = frameAnalyses
                .SelectMany(f => f.DetectedTexts)
                .Where(t => !string.IsNullOrWhiteSpace(t.Text))
                .GroupBy(t => t.Text.ToLower())
                .OrderByDescending(g => g.Count())
                .Take(20)
                .Select(g => g.Key)
                .ToList();

            summary.PlaceInfos = frameAnalyses
                .SelectMany(f => f.PlaceInfos)
                .Where(p => p.Confidence > 0.3)
                .GroupBy(p => p.Name?.ToLower())
                .Where(g => !string.IsNullOrWhiteSpace(g.Key))
                .Select(g => g.OrderByDescending(p => p.Confidence).First())
                .OrderByDescending(p => p.Confidence)
                .Take(10)
                .ToList();

            summary.SceneChanges = frameAnalyses
                .Where((f, i) => i > 0 && 
                    !string.Equals(f.Scene.Setting, frameAnalyses[i-1].Scene.Setting, StringComparison.OrdinalIgnoreCase))
                .Select(f => new SceneChange
                {
                    Timestamp = f.Timestamp,
                    Description = $"Scene change to: {f.Scene.Setting}",
                    ConfidenceScore = f.ConfidenceScore
                })
                .ToList();

            // 使用 Google Places API 豐富餐廳資訊
            await EnrichPlaceInfoWithGooglePlacesAsync(summary, cancellationToken);

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating video summary");
            return new VideoSummary
            {
                OverallDescription = "Unable to generate summary due to analysis error."
            };
        }
    }

    /// <summary>
    /// 生成截圖分析結果摘要
    /// </summary>
    private FrameAnalysisResult GenerateFrameAnalysisSummary(List<FrameAnalysis> frameAnalyses)
    {
        var result = new FrameAnalysisResult();

        if (!frameAnalyses.Any())
        {
            result.Summary = "無法分析截圖內容";
            return result;
        }

        // 生成截圖分析專用的摘要描述
        var sceneDescriptions = frameAnalyses
            .Where(f => !string.IsNullOrWhiteSpace(f.Scene.MainDescription))
            .Select(f => f.Scene.MainDescription)
            .Take(5)
            .ToList();
        
        result.Summary = sceneDescriptions.Any() 
            ? $"從 {frameAnalyses.Count} 張截圖中分析到：{string.Join("；", sceneDescriptions)}"
            : $"分析了 {frameAnalyses.Count} 張截圖";

        // 詳細描述
        result.DetailedDescription = GenerateFrameAnalysisDetailedDescription(frameAnalyses);

        // 活動統計
        result.MainActivities = frameAnalyses
            .SelectMany(f => f.Scene.Activities)
            .Where(a => !string.IsNullOrWhiteSpace(a))
            .GroupBy(a => a.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();

        // 物件檢測統計
        result.DetectedObjects = frameAnalyses
            .SelectMany(f => f.Scene.Objects)
            .Where(o => !string.IsNullOrWhiteSpace(o.Name))
            .GroupBy(o => o.Name.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(15)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();

        // 文字檢測統計
        result.DetectedText = frameAnalyses
            .SelectMany(f => f.DetectedTexts)
            .Where(t => !string.IsNullOrWhiteSpace(t.Text))
            .GroupBy(t => t.Text.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(20)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();

        // 場所資訊統計
        result.PlaceInfos = frameAnalyses
            .SelectMany(f => f.PlaceInfos)
            .Where(p => p.Confidence > 0.3 && !string.IsNullOrWhiteSpace(p.Name))
            .GroupBy(p => p.Name?.ToLower())
            .Select(g => g.OrderByDescending(p => p.Confidence).First())
            .OrderByDescending(p => p.Confidence)
            .Take(10)
            .ToList();

        // 場景變化統計
        result.SceneChanges = frameAnalyses
            .Where((f, i) => i > 0 && 
                !string.Equals(f.Scene.Setting, frameAnalyses[i-1].Scene.Setting, StringComparison.OrdinalIgnoreCase))
            .Select(f => new SceneChange
            {
                Timestamp = f.Timestamp,
                Description = $"場景切換至：{f.Scene.Setting}",
                ConfidenceScore = f.ConfidenceScore
            })
            .ToList();

        return result;
    }

    /// <summary>
    /// 生成字幕分析結果摘要
    /// </summary>
    private SubtitleAnalysisResult GenerateSubtitleAnalysisSummary(List<SubtitleSegment> subtitleSegments)
    {
        var result = new SubtitleAnalysisResult();

        if (!subtitleSegments.Any())
        {
            result.Summary = "無字幕內容可供分析";
            return result;
        }

        // 統計字幕基本資訊
        var totalDuration = subtitleSegments.LastOrDefault()?.EndTime ?? TimeSpan.Zero;
        var totalWordCount = subtitleSegments.Sum(s => s.Text?.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length ?? 0);

        result.Summary = $"分析了 {subtitleSegments.Count} 段字幕，總時長 {totalDuration:mm\\:ss}，約 {totalWordCount} 個詞";

        // 生成詳細描述
        result.DetailedDescription = GenerateSubtitleAnalysisDetailedDescription(subtitleSegments);

        // 主要話題提取
        result.MainTopics = ExtractMainTopicsFromSubtitles(subtitleSegments);

        // 關鍵要點提取
        result.KeyPoints = ExtractKeyPointsFromSubtitles(subtitleSegments);

        // 提及的地點
        result.MentionedPlaces = ExtractMentionedPlacesFromSubtitles(subtitleSegments);

        // 重要引語
        result.ImportantQuotes = ExtractImportantQuotesFromSubtitles(subtitleSegments);

        // 內容結構分析
        result.ContentStructure = AnalyzeSubtitleContentStructure(subtitleSegments);

        return result;
    }

    /// <summary>
    /// 生成截圖分析的詳細描述
    /// </summary>
    private string GenerateFrameAnalysisDetailedDescription(List<FrameAnalysis> frameAnalyses)
    {
        var descriptions = new List<string>();

        if (frameAnalyses.Any())
        {
            descriptions.Add($"總共分析了 {frameAnalyses.Count} 張關鍵幀");
            
            var successCount = frameAnalyses.Count(f => !string.IsNullOrWhiteSpace(f.Scene.MainDescription));
            descriptions.Add($"成功分析 {successCount} 張截圖 ({successCount * 100.0 / frameAnalyses.Count:F1}%)");

            if (frameAnalyses.Any(f => f.PlaceInfos.Any()))
            {
                var placeCount = frameAnalyses.Sum(f => f.PlaceInfos.Count);
                descriptions.Add($"識別到 {placeCount} 個場所資訊");
            }

            if (frameAnalyses.Any(f => f.DetectedTexts.Any()))
            {
                var textCount = frameAnalyses.Sum(f => f.DetectedTexts.Count);
                descriptions.Add($"檢測到 {textCount} 處文字內容");
            }
        }

        return string.Join("；", descriptions);
    }

    /// <summary>
    /// 生成字幕分析的詳細描述
    /// </summary>
    private string GenerateSubtitleAnalysisDetailedDescription(List<SubtitleSegment> subtitleSegments)
    {
        var descriptions = new List<string>();

        if (subtitleSegments.Any())
        {
            var analyzedCount = subtitleSegments.Count(s => s.Analysis != null && s.Analysis.Topics.Any());
            descriptions.Add($"總共 {subtitleSegments.Count} 段字幕，已分析 {analyzedCount} 段");

            var avgLength = subtitleSegments.Average(s => s.Text?.Length ?? 0);
            descriptions.Add($"平均長度 {avgLength:F0} 字元");

            if (subtitleSegments.Any(s => !string.IsNullOrWhiteSpace(s.Analysis?.Sentiment)))
            {
                var sentiments = subtitleSegments
                    .Where(s => !string.IsNullOrWhiteSpace(s.Analysis?.Sentiment))
                    .GroupBy(s => s.Analysis.Sentiment)
                    .OrderByDescending(g => g.Count())
                    .Take(3)
                    .Select(g => $"{g.Key}({g.Count()})")
                    .ToList();
                descriptions.Add($"情感傾向：{string.Join("、", sentiments)}");
            }
        }

        return string.Join("；", descriptions);
    }

    /// <summary>
    /// 從字幕中提取主要話題
    /// </summary>
    private List<string> ExtractMainTopicsFromSubtitles(List<SubtitleSegment> subtitleSegments)
    {
        return subtitleSegments
            .Where(s => s.Analysis != null && s.Analysis.Topics.Any())
            .SelectMany(s => s.Analysis.Topics)
            .Where(topic => !string.IsNullOrWhiteSpace(topic))
            .GroupBy(topic => topic.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();
    }

    /// <summary>
    /// 從字幕中提取關鍵要點
    /// </summary>
    private List<string> ExtractKeyPointsFromSubtitles(List<SubtitleSegment> subtitleSegments)
    {
        return subtitleSegments
            .Where(s => s.Analysis != null && s.Analysis.Keywords.Any())
            .SelectMany(s => s.Analysis.Keywords)
            .Where(kw => !string.IsNullOrWhiteSpace(kw))
            .GroupBy(kw => kw.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(15)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();
    }

    /// <summary>
    /// 從字幕中提取提及的地點
    /// </summary>
    private List<string> ExtractMentionedPlacesFromSubtitles(List<SubtitleSegment> subtitleSegments)
    {
        var places = new List<string>();
        var placeKeywords = new[] { "餐廳", "店", "館", "樓", "街", "路", "區", "市", "縣", "國", "地方", "這裡", "那裡" };

        foreach (var segment in subtitleSegments.Where(s => !string.IsNullOrWhiteSpace(s.Text)))
        {
            var words = segment.Text.Split(new char[] { ' ', '，', '。', '！', '？' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var word in words)
            {
                if (placeKeywords.Any(keyword => word.Contains(keyword)) && word.Length > 1)
                {
                    places.Add(word);
                }
            }
        }

        return places
            .GroupBy(p => p.ToLower())
            .OrderByDescending(g => g.Count())
            .Take(10)
            .Select(g => $"{g.Key} ({g.Count()})")
            .ToList();
    }

    /// <summary>
    /// 從字幕中提取重要引語
    /// </summary>
    private List<string> ExtractImportantQuotesFromSubtitles(List<SubtitleSegment> subtitleSegments)
    {
        return subtitleSegments
            .Where(s => !string.IsNullOrWhiteSpace(s.Text) && s.Text.Length > 20 && s.Text.Length < 100)
            .Where(s => s.Text.Contains("很") || s.Text.Contains("非常") || s.Text.Contains("真的") || 
                       s.Text.Contains("推薦") || s.Text.Contains("喜歡") || s.Text.Contains("好吃"))
            .Take(5)
            .Select(s => $"[{s.StartTime:mm\\:ss}] {s.Text}")
            .ToList();
    }

    /// <summary>
    /// 分析字幕內容結構
    /// </summary>
    private string AnalyzeSubtitleContentStructure(List<SubtitleSegment> subtitleSegments)
    {
        if (!subtitleSegments.Any()) return "無內容結構";

        var totalDuration = subtitleSegments.LastOrDefault()?.EndTime ?? TimeSpan.Zero;
        var avgSegmentDuration = subtitleSegments.Average(s => (s.EndTime - s.StartTime).TotalSeconds);

        var structure = new List<string>();
        structure.Add($"時長：{totalDuration:mm\\:ss}");
        structure.Add($"平均每段：{avgSegmentDuration:F1}秒");

        // 分析內容密度
        var wordsPerMinute = subtitleSegments.Sum(s => s.Text?.Split(' ').Length ?? 0) / Math.Max(1, totalDuration.TotalMinutes);
        structure.Add($"語速：{wordsPerMinute:F0}詞/分鐘");

        return string.Join("；", structure);
    }

    /// <summary>
    /// 從截圖分析結果中提取潛在的餐廳名稱
    /// </summary>
    private List<string> ExtractRestaurantNamesFromFrameAnalysis(FrameAnalysisResult frameAnalysis)
    {
        var restaurantNames = new HashSet<string>();

        // 從場所資訊中提取
        foreach (var place in frameAnalysis.PlaceInfos.Where(p => !string.IsNullOrWhiteSpace(p.Name)))
        {
            if (!string.IsNullOrWhiteSpace(place.Name))
                restaurantNames.Add(place.Name);
        }

        // 從檢測到的文字中提取可能的餐廳名稱
        foreach (var textItem in frameAnalysis.DetectedText)
        {
            // 移除統計數字括號部分，只保留文字內容
            var cleanText = textItem.Split('(')[0].Trim();
            if (IsLikelyRestaurantName(cleanText))
            {
                restaurantNames.Add(cleanText);
            }
        }

        return restaurantNames.Where(name => name.Length > 1 && name.Length < 50).ToList();
    }

    /// <summary>
    /// 從字幕分析結果中提取潛在的餐廳名稱
    /// </summary>
    private List<string> ExtractRestaurantNamesFromSubtitleAnalysis(SubtitleAnalysisResult subtitleAnalysis)
    {
        var restaurantNames = new HashSet<string>();

        // 從提及的地點中提取
        foreach (var place in subtitleAnalysis.MentionedPlaces)
        {
            var cleanText = place.Split('(')[0].Trim();
            if (IsLikelyRestaurantNameFromSubtitle(cleanText))
            {
                restaurantNames.Add(cleanText);
            }
        }

        // 從關鍵要點中提取包含餐廳相關詞彙的內容
        var restaurantKeywords = new[] { "餐廳", "店", "館", "屋", "料理", "食堂", "咖啡", "茶", "飲", "燒肉", "火鍋", "壽司", "拉麵", "麵", "粥", "湯", "小吃" };
        foreach (var keyPoint in subtitleAnalysis.KeyPoints)
        {
            var cleanText = keyPoint.Split('(')[0].Trim();
            if (restaurantKeywords.Any(keyword => cleanText.Contains(keyword)) && cleanText.Length > 2)
            {
                restaurantNames.Add(cleanText);
            }
        }

        return restaurantNames.Where(name => name.Length > 1 && name.Length < 50).ToList();
    }

    /// <summary>
    /// 判斷字幕中的文字是否可能是餐廳名稱
    /// </summary>
    private static bool IsLikelyRestaurantNameFromSubtitle(string text)
    {
        if (string.IsNullOrWhiteSpace(text) || text.Length <= 1)
            return false;

        // 餐廳相關關鍵字
        var restaurantKeywords = new[] { "餐廳", "店", "館", "屋", "料理", "食堂", "咖啡", "茶屋", "燒肉", "火鍋", "壽司", "拉麵", "麵屋", "小吃" };
        var foodKeywords = new[] { "美食", "料理", "菜", "飯", "麵", "湯", "茶", "咖啡", "甜點" };

        // 包含餐廳關鍵字的更可能是店名
        if (restaurantKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            return true;

        // 包含食物關鍵字且長度適中
        if (foodKeywords.Any(keyword => text.Contains(keyword, StringComparison.OrdinalIgnoreCase)) && text.Length >= 2 && text.Length <= 20)
            return true;

        return false;
    }

    /// <summary>
    /// 合併並去重來自不同來源的餐廳名稱
    /// </summary>
    private List<string> MergeRestaurantNames(List<string> frameNames, List<string> subtitleNames)
    {
        var allNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        
        foreach (var name in frameNames.Concat(subtitleNames))
        {
            var cleanName = name.Trim();
            if (!string.IsNullOrWhiteSpace(cleanName) && cleanName.Length > 1)
            {
                allNames.Add(cleanName);
            }
        }

        return allNames.OrderBy(name => name.Length).ToList(); // 較短的名稱優先，通常更精確
    }

    /// <summary>
    /// 使用 Google Places API 豐富餐廳資訊
    /// </summary>
    private async Task EnrichPlaceInfoWithGooglePlacesAsync(VideoSummary summary, CancellationToken cancellationToken = default)
    {
        if (!_placeDetectionService.IsEnabled)
        {
            _logger.LogInformation("Google Places API is not enabled, skipping place enrichment");
            return;
        }

        try
        {
            // 從截圖分析結果提取餐廳名稱
            var frameRestaurantNames = ExtractRestaurantNamesFromFrameAnalysis(summary.FrameAnalysisResult);
            
            // 從字幕分析結果提取餐廳名稱
            var subtitleRestaurantNames = ExtractRestaurantNamesFromSubtitleAnalysis(summary.SubtitleAnalysisResult);
            
            // 合併所有餐廳名稱
            var allRestaurantNames = MergeRestaurantNames(frameRestaurantNames, subtitleRestaurantNames);
            
            if (!allRestaurantNames.Any())
            {
                _logger.LogInformation("No restaurant names found for Google Places enrichment");
                return;
            }

            _logger.LogInformation("Found {Count} potential restaurant names for Google Places enrichment: {Names}", 
                allRestaurantNames.Count, string.Join(", ", allRestaurantNames.Take(5)));

            var enrichedPlaces = new List<PlaceInfo>();
            
            // 對每個餐廳名稱進行 Google Places 查詢
            foreach (var restaurantName in allRestaurantNames.Take(10)) // 限制查詢數量避免過度使用 API
            {
                try
                {
                    var placeReferences = await _placeDetectionService.DetectPlacesFromTextAsync(restaurantName, "video_analysis", cancellationToken);
                    
                    foreach (var placeRef in placeReferences.Take(3)) // 每個名稱最多取3個結果
                    {
                        // 獲取詳細資訊
                        if (!string.IsNullOrWhiteSpace(placeRef.PlaceId))
                        {
                            var detailedPlace = await _placeDetectionService.GetPlaceDetailsAsync(placeRef.PlaceId, cancellationToken);
                        
                            if (detailedPlace != null)
                            {
                                // 轉換為 PlaceInfo 並加入到豐富化列表
                                var enrichedPlace = ConvertPlaceReferenceToPlaceInfo(detailedPlace, restaurantName);
                                enrichedPlaces.Add(enrichedPlace);
                                
                                _logger.LogDebug("Enriched place info for '{Name}': {PlaceName} at {Address}", 
                                    restaurantName, detailedPlace.Name, detailedPlace.FormattedAddress);
                            }
                        }
                    }
                    
                    // 添加短暫延遲避免過快的 API 調用
                    await Task.Delay(500, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to enrich place info for restaurant name: {Name}", restaurantName);
                }
            }

            // 合併豐富化的場所資訊到現有的 PlaceInfos 中
            if (enrichedPlaces.Any())
            {
                summary.PlaceInfos = MergePlaceInfos(summary.PlaceInfos, enrichedPlaces);
                summary.FrameAnalysisResult.PlaceInfos = MergePlaceInfos(summary.FrameAnalysisResult.PlaceInfos, enrichedPlaces);
                
                _logger.LogInformation("Successfully enriched {Count} places with Google Places API data", enrichedPlaces.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Google Places API enrichment");
        }
    }

    /// <summary>
    /// 將 PlaceReference 轉換為 PlaceInfo
    /// </summary>
    private PlaceInfo ConvertPlaceReferenceToPlaceInfo(PlaceReference placeRef, string originalName)
    {
        // 將 PriceLevel 字串轉換為數字
        int? priceLevelNumeric = null;
        if (!string.IsNullOrWhiteSpace(placeRef.PriceLevel))
        {
            priceLevelNumeric = placeRef.PriceLevel switch
            {
                "免費" => 0,
                "便宜" => 1,
                "中等" => 2,
                "昂貴" => 3,
                "非常昂貴" => 4,
                _ => null
            };
        }

        return new PlaceInfo
        {
            Name = placeRef.Name ?? originalName,
            Category = placeRef.Types?.FirstOrDefault() ?? "restaurant",
            Description = $"Google Places 資訊：{placeRef.FormattedAddress}",
            Confidence = Math.Max(0.9, placeRef.ConfidenceScore), // 來自 Google Places API 的資料信心度較高
            OriginalTexts = [originalName],
            Address = placeRef.FormattedAddress,
            BusinessHours = placeRef.OpeningHours?.FirstOrDefault(),
            Phone = placeRef.PhoneNumber,
            Website = placeRef.Website,
            // Google Places 特有欄位
            GooglePlaceId = placeRef.PlaceId,
            GoogleRating = placeRef.Rating,
            GooglePriceLevel = priceLevelNumeric,
            GoogleUserRatingsTotal = placeRef.UserRatingsTotal,
            GooglePhotoReferences = new List<string>() // 目前 PlaceReference 沒有照片資料
        };
    }

    /// <summary>
    /// 合併場所資訊列表，去除重複項目
    /// </summary>
    private List<PlaceInfo> MergePlaceInfos(List<PlaceInfo> existingPlaces, List<PlaceInfo> newPlaces)
    {
        var merged = new List<PlaceInfo>(existingPlaces);
        
        foreach (var newPlace in newPlaces)
        {
            // 檢查是否已存在相似的場所（基於名稱相似度）
            var existingSimilar = merged.FirstOrDefault(existing => 
                !string.IsNullOrWhiteSpace(existing.Name) &&
                !string.IsNullOrWhiteSpace(newPlace.Name) &&
                (existing.Name.Contains(newPlace.Name, StringComparison.OrdinalIgnoreCase) ||
                 newPlace.Name.Contains(existing.Name, StringComparison.OrdinalIgnoreCase) ||
                 LevenshteinDistance(existing.Name.ToLower(), newPlace.Name.ToLower()) <= 2));

            if (existingSimilar != null)
            {
                // 更新現有項目為更詳細的資訊
                if (newPlace.Confidence > existingSimilar.Confidence || !string.IsNullOrWhiteSpace(newPlace.GooglePlaceId))
                {
                    merged.Remove(existingSimilar);
                    merged.Add(newPlace);
                }
            }
            else
            {
                merged.Add(newPlace);
            }
        }
        
        return merged.OrderByDescending(p => p.Confidence).ToList();
    }

    /// <summary>
    /// 計算兩個字串之間的編輯距離
    /// </summary>
    private static int LevenshteinDistance(string s1, string s2)
    {
        var matrix = new int[s1.Length + 1, s2.Length + 1];

        for (int i = 0; i <= s1.Length; i++)
            matrix[i, 0] = i;
        for (int j = 0; j <= s2.Length; j++)
            matrix[0, j] = j;

        for (int i = 1; i <= s1.Length; i++)
        {
            for (int j = 1; j <= s2.Length; j++)
            {
                var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                matrix[i, j] = Math.Min(Math.Min(
                    matrix[i - 1, j] + 1,
                    matrix[i, j - 1] + 1),
                    matrix[i - 1, j - 1] + cost);
            }
        }

        return matrix[s1.Length, s2.Length];
    }

    private PerformanceMetrics CalculatePerformanceMetrics(List<FrameAnalysis> frameAnalyses, DateTime analysisStartTime, DateTime analysisEndTime)
    {
        var metrics = new PerformanceMetrics
        {
            TotalAnalysisTime = analysisEndTime - analysisStartTime,
            TotalFramesAnalyzed = frameAnalyses.Count
        };

        var validAnalyses = frameAnalyses.Where(f => f.AnalysisDuration > TimeSpan.Zero).ToList();
        
        if (validAnalyses.Any())
        {
            var durations = validAnalyses.Select(f => f.AnalysisDuration).ToList();
            
            metrics.AverageFrameAnalysisTime = TimeSpan.FromMilliseconds(durations.Average(d => d.TotalMilliseconds));
            metrics.FastestFrameAnalysisTime = durations.Min();
            metrics.SlowestFrameAnalysisTime = durations.Max();
            metrics.SuccessfulAnalyses = validAnalyses.Count;
            metrics.FailedAnalyses = frameAnalyses.Count - validAnalyses.Count;

            // Create detailed timing info
            metrics.DetailedTimings = validAnalyses.Select(f => new AnalysisTimingInfo
            {
                Duration = f.AnalysisDuration,
                StartTime = f.AnalysisStartTime,
                EndTime = f.AnalysisEndTime,
                Stage = "FrameAnalysis"
            }).ToList();
        }
        else
        {
            metrics.FailedAnalyses = frameAnalyses.Count;
        }

        return metrics;
    }
}